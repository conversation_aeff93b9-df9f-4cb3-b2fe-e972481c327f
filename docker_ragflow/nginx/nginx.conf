user root;
worker_processes auto;

error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    #tcp_nopush on;

    # 超时控制 (单位：秒)
    keepalive_timeout 60;       # 减少长连接保持时间
    client_body_timeout 10;     # 客户端请求体超时
    client_header_timeout 10;   # 客户端请求头超时
    send_timeout 10;            # 响应传输超时

    # 连接限制配置
    limit_conn_zone $binary_remote_addr zone=perip:10m;  # 创建共享内存区
    limit_conn perip 100;         # 单IP最大并发连接数
    limit_rate 100k;            # 单连接速率限制

    # 安全增强
    server_tokens off;          # 隐藏Nginx版本号
    client_max_body_size 1024M; # 保持原大文件上传支持

    #gzip on;

    include /etc/nginx/conf.d/ragflow.conf;
}